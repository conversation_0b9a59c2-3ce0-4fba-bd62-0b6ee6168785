{"model_id": "doubao-seed-1-6-250615", "response_format": "text", "question_type": "单选题", "pinyin_name": "<PERSON><PERSON><PERSON><PERSON>", "images_dir": "images", "question_dir": "types\\danxuanti", "use_enhance": false, "scale": 1, "use_pixel_connection": false, "grading_mode": "json_compare", "test2_prompt": null, "test3_prompt": "你是一位严谨负责的资深阅卷老师，现在要为一位学生批改作业。你的任务是严格对照正确答案，比对学生的答案，然后给出每个题目的对错情况，最终以JSON格式输出结果，JSON的键为“题目1”“题目2”……（按题号顺序编号，且必须始终从“题目1”开始，依次递增），值只可能为true或者false。\n以下是学生的答案：\n<student_answers>\n{{STUDENT_ANSWERS}}\n</student_answers>\n以下是正确答案：\n<correct_answers>\n{{CORRECT_ANSWERS}}\n</correct_answers>\n比对规则如下：\n\n- 逐一对比学生答案和正确答案中相同位置的题目答案。\n- 如果两个答案相同，则该题目对应返回true；如果不同，则返回false。\n\n例如，若学生答案json为{\"题目1\": \"B\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"B\"}，正确答案为{\"题目1\": \"A\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"C\"}，则返回{\"题目1\": false, \"题目2\": true, \"题目3\": true, \"题目4\": true, \"题目5\": false}。", "temperature": null, "top_p": null, "max_tokens": null, "gray_threshold": null}