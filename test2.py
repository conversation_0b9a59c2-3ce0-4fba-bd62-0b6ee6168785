import os
import json
import datetime
import re
import markdown
from bs4 import BeautifulSoup
from pypinyin import pinyin, Style
import multiprocessing # Import multiprocessing module
import sys # sys is needed for sys.path.append and sys.exit
import time  # 新增：用于记录时间

# Ensure volcengine-python-sdk-master is in sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'volcengine-python-sdk-master'))

from volcenginesdkarkruntime import Ark
from image_utils import image_to_base64, validate_base64
from yolo_utils import parse_yolo_annotation
from ai_utils import extract_json_from_response

# --- Auxiliary Functions (Unchanged from your provided code) ---

def markdown_to_text(markdown_content):
    """将markdown格式转换为纯文本"""
    # 将markdown转换为HTML
    html = markdown.markdown(markdown_content)
    # 使用BeautifulSoup提取纯文本
    soup = BeautifulSoup(html, 'html.parser')
    # 获取纯文本内容
    text = soup.get_text()
    # 清理多余的空白字符
    text = re.sub(r'\n\s*\n', '\n\n', text)  # 将多个空行替换为两个换行
    text = re.sub(r'[ \t]+', ' ', text)  # 将多个空格替换为单个空格
    return text.strip()

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_model_choice():
    """让用户选择模型ID"""
    available_models = {
        "1": "doubao-seed-1-6-250615",
        "2": "doubao-seed-1-6-flash-250715",
        "3": "doubao-1-5-thinking-vision-pro-250428",
        "4": "doubao-1-5-vision-pro-32k-250115"
    }

    print("请选择模型ID：")
    for key, value in available_models.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入模型编号（1-4）：").strip()
        if user_input in available_models:
            selected_model = available_models[user_input]
            print(f"选择的模型：{selected_model}")
            return selected_model
        else:
            print("输入无效，请输入 1-4 的数字")


def get_response_format_choice(model_id):
    """根据模型ID判断是否支持jsonObject，如果支持则让用户选择response_format"""
    # 支持jsonObject的模型列表（模型1、2、3）
    json_object_supported_models = [
        "doubao-seed-1-6-250615",
        "doubao-seed-1-6-flash-250715",
        "doubao-1-5-thinking-vision-pro-250428"
    ]

    if model_id in json_object_supported_models:
        print("选择response_format：")
        print("1. text")
        print("2. json_object")

        while True:
            user_input = input("请输入选择（1-2）：").strip()
            if user_input == "1":
                return "text"
            elif user_input == "2":
                return "json_object"
            else:
                print("输入无效，请输入 1 或 2")
    else:
        return "text"  # 不支持jsonObject的模型默认使用text


def get_max_tokens_for_model(model_id):
    """根据模型ID返回对应的max_tokens值"""
    if model_id == "doubao-1-5-vision-pro-32k-250115":  # 模型4
        return 12288  # 12K
    else:  # 模型1、2、3
        return 16384  # 16K

def get_grading_mode():
    """让用户选择批改模式"""
    print("请选择批改模式：")
    print("1. 使用大模型批改")
    print("2. 使用JSON比对")

    while True:
        user_input = input("请输入模式编号（1-2）：").strip()
        if user_input == "1":
            print("选择的模式：大模型批改")
            return "model"
        elif user_input == "2":
            print("选择的模式：JSON比对")
            return "json_compare"
        else:
            print("输入无效，请输入 1 或 2")

def get_question_type():
    """获取用户输入的题型并转换为拼音路径"""
    question_types = {
        "1": "涂卡选择题", "2": "涂卡判断题", "3": "连线题",
        "4": "图表题", "5": "翻译题", "6": "画图题",
        "7": "数学应用题", "8": "数学计算题", "9": "简单的四则运算",
        "10": "填空题", "11": "判断题", "12": "多选题", "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）或直接输入中文：").strip()

        # 检查数字输入
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        # 检查中文输入
        elif user_input in question_types.values():
            question_type = user_input
            break
        else:
            print("输入无效，请输入 1-13 的数字或直接输入中文题型名称")

    # 转换为拼音
    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")

    return question_type, pinyin_name

def extract_json_responses(md_path):
    """提取md文件中所有响应内容的JSON"""
    results = []
    with open(md_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # 匹配所有 ### 响应内容： 后面紧跟的代码块内容
    resp_pattern = r"### 响应内容：\s*```json\s*([\s\S]*?)\s*```"
    resp_matches = re.findall(resp_pattern, content)
    
    for match in resp_matches:
        json_str = match.strip()
        try:
            # 验证JSON格式
            json.loads(json_str) # Just load to validate, don't store object
            results.append(json_str)
        except json.JSONDecodeError as e: # More specific error handling
            print(f"警告：跳过无效的JSON格式 '{json_str[:50]}...' (错误: {str(e)})")
        except Exception as e:
            print(f"警告：跳过无效的JSON格式，未知错误: {str(e)}")
    
    return results

def get_latest_md_file(response_dir):
    """获取response文件夹中时间最晚的md文件（排除answer.md和response_template.md）"""
    md_files = []
    if not os.path.exists(response_dir):
        return None
    for filename in os.listdir(response_dir):
        if filename.endswith('.md') and filename not in ['answer.md', 'response_template.md']:
            md_files.append(filename)
    
    if not md_files:
        return None
    
    # Sort by filename (which includes timestamp), return the latest
    md_files.sort()
    return os.path.join(response_dir, md_files[-1])

# --- JSON比对处理函数 ---
def process_single_json_pair_compare(test_json, answer_json, index):
    """
    使用JSON比对方式处理单对JSON响应
    """
    sep = f"\n{'='*50}\n"
    info = f"处理第 {index} 组JSON响应"

    print(f"正在比对：第 {index} 组JSON响应")

    current_output_lines = []
    current_output_lines.append(sep)
    current_output_lines.append(info + "\n")
    current_output_lines.append(sep)

    try:
        # 记录开始时间
        start_time = time.time()

        # 解析学生答案和正确答案的JSON
        try:
            student_answers = json.loads(test_json.strip())
            correct_answers = json.loads(answer_json.strip())
        except json.JSONDecodeError as e:
            raise Exception(f"JSON解析失败: {str(e)}")

        # 逐题比对，生成true/false结果
        compare_result = {}

        # 获取所有题目（以正确答案为准）
        for question_key in correct_answers.keys():
            if question_key in student_answers:
                # 比较学生答案和正确答案
                compare_result[question_key] = student_answers[question_key] == correct_answers[question_key]
            else:
                # 学生答案中没有这道题，标记为false
                compare_result[question_key] = False

        # 检查学生答案中是否有多余的题目
        for question_key in student_answers.keys():
            if question_key not in correct_answers:
                compare_result[question_key] = False

        # 记录结束时间并计算响应时间
        end_time = time.time()
        response_time = end_time - start_time

        # 按照题号数字顺序排序
        def extract_question_number(key):
            import re
            match = re.search(r'\d+', str(key))
            if match:
                return int(match.group())
            return 0

        sorted_items = sorted(compare_result.items(), key=lambda x: extract_question_number(x[0]))
        sorted_compare_result = dict(sorted_items)

        # 格式化比对结果为JSON字符串
        resp_content = json.dumps(sorted_compare_result, ensure_ascii=False, separators=(',', ':'))

        # Append results to output lines for this specific task
        current_output_lines.append(f"### 学生答案：\n")
        current_output_lines.append("```json\n")
        current_output_lines.append(f"{test_json}\n")
        current_output_lines.append("```\n\n")

        current_output_lines.append(f"### 正确答案：\n")
        current_output_lines.append("```json\n")
        current_output_lines.append(f"{answer_json}\n")
        current_output_lines.append("```\n\n")

        current_output_lines.append(f"### 比对结果：\n")
        current_output_lines.append("```json\n")
        current_output_lines.append(f"{resp_content}\n")
        current_output_lines.append("```\n")
        # 添加响应时间记录
        current_output_lines.append(f"### 响应时间：{response_time:.4f}秒\n")

        return {
            'success': True,
            'index': index,
            'output_lines': current_output_lines,
            'model_response_json': resp_content
        }
    except Exception as e:
        err_msg = f"处理第 {index} 组JSON响应时出错: {str(e)}"
        print(f"比对错误: {err_msg}")
        current_output_lines.append(err_msg + "\n")
        return {
            'success': False,
            'index': index,
            'output_lines': current_output_lines,
            'model_response_json': None
        }

# --- 关键修改：并行处理函数 `process_single_json_pair_parallel` 的定义 ---
# 此函数定义在 `if __name__ == "__main__":` 之外，以兼容多进程
# 参数列表已修改，直接接收 5 个参数
def process_single_json_pair_parallel(test_json, answer_json, round2_prompt_without_images_text, index, client_api_key, model_id, response_format="text", temperature=1, top_p=0.7, max_tokens=None):
    """
    Processes a single pair of JSON responses with the model in parallel.
    """
    # 在子进程中初始化 Ark 客户端
    client_local = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=client_api_key,
    )

    sep = f"\n{'='*50}\n"
    info = f"处理第 {index} 组JSON响应"

    print(f"进程 {os.getpid()} 正在处理：第 {index} 组JSON响应")

    current_output_lines = []
    current_output_lines.append(sep)
    current_output_lines.append(info + "\n")
    current_output_lines.append(sep)

    try:
        # Construct request content
        content = [
            {"type": "text", "text": test_json},
            {"type": "text", "text": answer_json},
            {"type": "text", "text": round2_prompt_without_images_text}
        ]

        # 记录开始时间
        start_time = time.time()

        # 构建未加密的显示版本内容（与实际content格式保持一致）
        content_display = [
            {"type": "text", "text": test_json},
            {"type": "text", "text": answer_json},
            {"type": "text", "text": round2_prompt_without_images_text}
        ]

        # 构建请求参数（先保存未加密版本用于显示）
        request_params_display = {
            "model": model_id,
            "messages": [
                {
                    "role": "user",
                    "content": content_display
                }
            ],
            "temperature": temperature,
            "top_p": top_p,
            "max_tokens": max_tokens if max_tokens is not None else get_max_tokens_for_model(model_id),
            "thinking": {"type": "disabled"}
        }

        # 构建实际请求参数（包含加密头）
        request_params = {
            "model": model_id,
            "messages": [
                {
                    "role": "user",
                    "content": content
                }
            ],
            "extra_headers": {'x-is-encrypted': 'true'},
            "temperature": temperature,
            "top_p": top_p,
            "max_tokens": max_tokens if max_tokens is not None else get_max_tokens_for_model(model_id),
            "thinking": {"type": "disabled"}
        }

        # 如果选择了json_object格式，添加response_format参数
        if response_format == "json_object":
            request_params["response_format"] = {"type": "json_object"}
            request_params_display["response_format"] = {"type": "json_object"}

        response = client_local.chat.completions.create(**request_params)
        # 记录结束时间并计算响应时间
        end_time = time.time()
        response_time = end_time - start_time
        
        # Ensure response content format is consistent
        resp_content = response.choices[0].message.content.strip()
        if resp_content.startswith("```json"):
            resp_content = resp_content[7:]
        if resp_content.startswith("```"):
            resp_content = resp_content[3:]
        if resp_content.endswith("```"):
            resp_content = resp_content[:-3]
        resp_content = resp_content.strip()
        
        # Append results to output lines for this specific task
        current_output_lines.append(f"### 学生答案：\n")
        current_output_lines.append("```json\n")
        current_output_lines.append(f"{test_json}\n")
        current_output_lines.append("```\n\n")
        
        current_output_lines.append(f"### 正确答案：\n")
        current_output_lines.append("```json\n")
        current_output_lines.append(f"{answer_json}\n")
        current_output_lines.append("```\n\n")
        
        current_output_lines.append(f"### 模型回答：\n")
        current_output_lines.append("```json\n")
        current_output_lines.append(f"{resp_content}\n")
        current_output_lines.append("```\n")

        # 添加请求体（使用未加密版本，test2.py中没有图片）
        current_output_lines.append(f"### 请求体：\n")
        current_output_lines.append("```json\n")
        current_output_lines.append(f"{json.dumps(request_params_display, ensure_ascii=False, indent=2)}\n")
        current_output_lines.append("```\n")

        # 添加响应时间记录
        current_output_lines.append(f"### 响应时间：{response_time:.2f}秒\n")

        return {
            'success': True,
            'index': index,
            'output_lines': current_output_lines,
            'model_response_json': resp_content # To be used for comparison later if needed
        }
    except Exception as e:
        err_msg = f"处理第 {index} 组JSON响应时出错: {str(e)}"
        print(f"进程 {os.getpid()} 错误: {err_msg}")
        current_output_lines.append(err_msg + "\n")
        return {
            'success': False,
            'index': index,
            'output_lines': current_output_lines,
            'model_response_json': None
        }

# --- Compare Wrong Questions Functions (Unchanged from your provided code) ---

def extract_json_responses_and_names(md_path):
    """提取md文件中所有响应名和响应内容（返回列表: [(response_name, json_str)]）"""
    results = []
    with open(md_path, "r", encoding="utf-8") as f:
        content = f.read()
    # 匹配所有"处理第 n 组JSON响应"
    resp_pattern = r"处理第 (\d+) 组JSON响应"
    resp_matches = re.findall(resp_pattern, content)
    # 匹配所有 ### 模型回答： 或 ### 比对结果： 后面紧跟的代码块内容
    model_pattern = r"### (?:模型回答|比对结果)：\s*```json\s*([\s\S]*?)\s*```"
    model_matches = re.findall(model_pattern, content)
    # 标准化json
    norm_jsons = []
    for m in model_matches:
        json_str = m.strip()
        try:
            obj = json.loads(json_str)
            # 按照题号数字顺序排序，而不是字符串顺序
            def extract_question_number(key):
                """从题号中提取数字部分"""
                import re
                # 匹配数字部分
                match = re.search(r'\d+', str(key))
                if match:
                    return int(match.group())
                return 0  # 如果没有数字，返回0

            # 按照题号数字排序
            sorted_items = sorted(obj.items(), key=lambda x: extract_question_number(x[0]))
            sorted_obj = dict(sorted_items)
            norm = json.dumps(sorted_obj, ensure_ascii=False, separators=(',', ':'))
            norm_jsons.append(norm)
        except Exception:
            norm_jsons.append(json_str)
    # 按顺序配对
    for i in range(max(len(resp_matches), len(norm_jsons))):
        resp_name = f"第{resp_matches[i]}组" if i < len(resp_matches) else None
        norm_json = norm_jsons[i] if i < len(norm_jsons) else None
        results.append((resp_name, norm_json))
    return results

# --- Main Program Execution ---

# Wrap the main program execution in a `if __name__ == "__main__":` block.
# This is crucial for multiprocessing, especially on Windows.
if __name__ == "__main__":
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='运行test2.py脚本')
    parser.add_argument('--config', help='配置文件路径')
    args = parser.parse_args()

    # Your API Key (ensure it's handled securely, e.g., from environment variables)
    api_key_from_client_init = "36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b"

    if args.config and os.path.exists(args.config):
        # 从配置文件加载参数
        print("从配置文件加载参数...")
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)

        grading_mode = config.get('grading_mode', 'model')  # 默认使用模型批改
        selected_model = config['model_id']
        response_format = config.get('response_format', 'text')  # 默认使用text格式
        question_type = config['question_type']
        pinyin_name = config['pinyin_name']
        custom_prompt = config.get('test2_prompt')  # 获取自定义prompt
        # 获取API参数，如果没有配置则使用默认值
        temperature = config.get('temperature', 1)
        top_p = config.get('top_p', 0.7)
        max_tokens = config.get('max_tokens')

        print(f"使用配置：批改模式={grading_mode}, 模型={selected_model}, response_format={response_format}, 题型={question_type}")
        if custom_prompt:
            print("将使用从main脚本传递的自定义提示词")
    else:
        # 交互模式获取参数
        print("配置文件不存在，使用交互模式...")

        # Get user-selected grading mode
        grading_mode = get_grading_mode()

        # Get user-selected model (only if using model grading)
        if grading_mode == "model":
            selected_model = get_model_choice()
            response_format = get_response_format_choice(selected_model)
        else:
            selected_model = "JSON比对模式"
            response_format = "text"

        # Get user-selected question type (Sequential)
        question_type, pinyin_name = get_question_type()
        custom_prompt = None  # 交互模式下没有自定义prompt
        # 交互模式下使用默认的API参数
        temperature = 1
        top_p = 0.7
        max_tokens = None

    # Build type-related paths (Sequential)
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)
    response_dir = os.path.join(question_dir, "response")
    round2_response_without_images_dir = os.path.join(question_dir, "round2_response_without_images")
    round2_prompt_without_images_file = os.path.join(question_dir, "round2_prompt_without_images.md")
    answer_file = os.path.join(response_dir, "answer.md")

    # Create round2_response_without_images folder (Sequential)
    os.makedirs(round2_response_without_images_dir, exist_ok=True)

    # Check for necessary files (Sequential)
    if not custom_prompt and not os.path.exists(round2_prompt_without_images_file):
        print(f"错误：round2_prompt_without_images文件 {round2_prompt_without_images_file} 不存在！")
        print(f"请创建 {round2_prompt_without_images_file} 文件并写入提示词内容")
        sys.exit()

    if not os.path.exists(answer_file):
        print(f"错误：answer文件 {answer_file} 不存在！")
        sys.exit()

    # Get the latest md file from response_dir (Sequential)
    latest_md_file = get_latest_md_file(response_dir)
    if not latest_md_file:
        print(f"错误：在 {response_dir} 中没有找到md文件！")
        sys.exit()

    print(f"找到时间最晚的md文件：{latest_md_file}")

    # 优先使用自定义prompt，否则从round2_prompt_without_images.md文件读取提示词
    if custom_prompt:
        round2_prompt_without_images_text = custom_prompt
        print("使用从main脚本传递的自定义提示词")
    else:
        try:
            with open(round2_prompt_without_images_file, 'r', encoding='utf-8') as f:
                round2_markdown_prompt = f.read().strip()
            print(f"已从文件 {round2_prompt_without_images_file} 读取round2_prompt_without_images")
            round2_prompt_without_images_text = markdown_to_text(round2_markdown_prompt)
            print("已将markdown格式转换为纯文本")
        except Exception as e:
            print(f"读取round2_prompt_without_images文件时出错：{str(e)}")
            sys.exit()

        if not round2_prompt_without_images_text:
            print("错误：round2_prompt_without_images文件为空！")
            sys.exit()

    print(f"使用的提示词: {round2_prompt_without_images_text}")

    # Extract JSON responses (Sequential)
    print("正在提取时间最晚的md文档中的JSON响应...")
    test_json_responses = extract_json_responses(latest_md_file)
    print(f"从时间最晚的md文档中提取到 {len(test_json_responses)} 个JSON响应")

    print("正在提取answer.md文档中的JSON响应...")
    answer_json_responses = extract_json_responses(answer_file)
    print(f"从answer.md文档中提取到 {len(answer_json_responses)} 个JSON响应")

    # Check JSON response count consistency (Sequential)
    if len(test_json_responses) != len(answer_json_responses):
        print(f"警告：时间最晚的md文档和answer.md文档中的JSON响应数量不一致！")
        print(f"时间最晚的md文档: {len(test_json_responses)}, answer: {len(answer_json_responses)}")
        min_count = min(len(test_json_responses), len(answer_json_responses))
        test_json_responses = test_json_responses[:min_count]
        answer_json_responses = answer_json_responses[:min_count]
        print(f"将使用前 {min_count} 个响应进行处理")

    # Generate round2_response_without_images file path (Sequential)
    now = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    round2_response_without_images_file = os.path.join(round2_response_without_images_dir, f"{now}.md")
    output_lines = []

    # Write header (Sequential) - 预留准确率位置
    output_lines.append(f"# 运行时间: {now}\n\n")
    if grading_mode == "model":
        output_lines.append(f"**批改方式：** 模型交互\n\n")
        output_lines.append(f"**使用模型ID：** {selected_model}\n\n")
        output_lines.append(f"## 使用的prompt\n\n")
        output_lines.append(f"{round2_prompt_without_images_text}\n\n")
    else:
        output_lines.append(f"**批改方式：** JSON比对\n\n")
        output_lines.append(f"**比对说明：** 直接比对学生答案和正确答案的JSON字符串\n\n")

    # --- 开始处理JSON响应对 ---
    if grading_mode == "model":
        print("\n--- 开始并行处理JSON响应对并与模型交互 ---\n")

        # Prepare tasks for parallel processing
        tasks = []
        for i, (test_json, answer_json) in enumerate(zip(test_json_responses, answer_json_responses), 1):
            # 确保这里传入的参数顺序和数量与 process_single_json_pair_parallel 的定义一致
            tasks.append((test_json, answer_json, round2_prompt_without_images_text, i, api_key_from_client_init, selected_model, response_format, temperature, top_p, max_tokens))

        # Set the number of processes
        num_processes = os.cpu_count() if os.cpu_count() else 4 # Default to 4 processes
        print(f"将使用 {num_processes} 个进程进行并行处理。")

        all_processed_results = []
        with multiprocessing.Pool(processes=num_processes) as pool:
            # starmap 会解包 tasks 中的每个元组，作为独立的参数传递给函数
            all_processed_results = pool.starmap(process_single_json_pair_parallel, tasks)

        print("\n--- 并行处理完成，合并结果 ---\n")
    else:
        print("\n--- 开始JSON比对处理 ---\n")

        all_processed_results = []
        for i, (test_json, answer_json) in enumerate(zip(test_json_responses, answer_json_responses), 1):
            result = process_single_json_pair_compare(test_json, answer_json, i)
            all_processed_results.append(result)

        print("\n--- JSON比对处理完成 ---\n")

    # Collect results in order
    for result in all_processed_results:
        output_lines.extend(result['output_lines'])

    output_lines.append(f"\n{'='*50}\n")
    output_lines.append("所有JSON响应处理完成！\n")
    output_lines.append(f"{'='*50}\n")
    print(f"\n{'='*50}\n")
    print("所有JSON响应处理完成！")
    print(f"{'='*50}\n")
    # --- 并行处理结束 ---

    # Write new content to the response file (Sequential)
    with open(round2_response_without_images_file, "w", encoding="utf-8") as f:
        f.writelines(output_lines)

    # If response_template.md doesn't exist, copy the current md file as a template (Sequential)
    template_path = os.path.join(round2_response_without_images_dir, "response_template.md")
    if not os.path.exists(template_path):
        import shutil
        shutil.copyfile(round2_response_without_images_file, template_path)
        print(f"已创建response_template.md模板文件")

    # Compare wrong questions functionality (Sequential)
    template_path = os.path.join(round2_response_without_images_dir, "response_template.md")
    wrong_items = []
    accuracy_str = ""
    if os.path.exists(template_path):
        new_results = extract_json_responses_and_names(round2_response_without_images_file)
        template_results = extract_json_responses_and_names(template_path)
        wrongs = []
        error_msgs = []
        
        # Check response count consistency
        if len(new_results) != len(template_results):
            error_msgs.append(f"响应数量不一致：本次{len(new_results)}，模板{len(template_results)}")
        
        min_len = min(len(new_results), len(template_results))
        for i in range(min_len):
            new_name, new_json = new_results[i]
            tpl_name, tpl_json = template_results[i]
            if new_name != tpl_name:
                wrongs.append(f"第 {i+1} 组响应: 响应名不一致，本次为 {new_name}，模板为 {tpl_name}")
                wrong_items.append(i+1)
            elif new_json != tpl_json:
                # 在比较前，对两个JSON进行值序列比较
                def compare_json_values(json_str1, json_str2):
                    """比较两个JSON的值序列，忽略键名差异"""
                    try:
                        obj1 = json.loads(json_str1)
                        obj2 = json.loads(json_str2)

                        # 按照题号数字顺序排序并提取值
                        def extract_question_number(key):
                            import re
                            match = re.search(r'\d+', str(key))
                            if match:
                                return int(match.group())
                            return 0

                        # 对两个JSON对象按题号排序并提取值序列
                        sorted_items1 = sorted(obj1.items(), key=lambda x: extract_question_number(x[0]))
                        sorted_items2 = sorted(obj2.items(), key=lambda x: extract_question_number(x[0]))

                        values1 = [item[1] for item in sorted_items1]
                        values2 = [item[1] for item in sorted_items2]

                        return values1 == values2
                    except:
                        # 如果JSON解析失败，回退到字符串比较
                        return json_str1.strip() == json_str2.strip()

                if not compare_json_values(new_json, tpl_json):
                    wrongs.append(f"第 {i+1} 组响应: {new_name}")
                    wrong_items.append(i+1)
                
        # Calculate accuracy
        total = min_len
        wrong_count = len(wrongs)
        accuracy = (total - wrong_count) / total if total > 0 else 1.0
        accuracy_str = f"准确率：{(accuracy*100):.2f}%  （({total} - {wrong_count}) / {total}）\n"
        
        # Construct new content for the top of the markdown file - 准确率置顶
        # 1. 准确率置顶
        new_top = f"## {accuracy_str}\n"

        # 2. 错题信息
        new_top += "## 错题\n"
        if error_msgs:
            for msg in error_msgs:
                new_top += f"- {msg}\n"
        if wrongs:
            for w in wrongs:
                new_top += f"- {w}\n"
        if not error_msgs and not wrongs:
            new_top += "本次无错题。\n"
        new_top += "\n"

        # Read old content and write new content
        with open(round2_response_without_images_file, "r", encoding="utf-8") as f:
            old_content = f.read()

        with open(round2_response_without_images_file, "w", encoding="utf-8") as f:
            f.write(new_top)
            f.write(old_content)
        print(new_top) # Print to console

        # Create summary file for wrong items
        if wrong_items:
            # Ensure round2_error_without_images directory exists
            round2_error_without_images_dir = os.path.join(question_dir, "round2_error_without_images")
            os.makedirs(round2_error_without_images_dir, exist_ok=True)
            summary_file = os.path.join(round2_error_without_images_dir, f"summary_{now}.md")
            # 读取 round2_response_without_images_file 内容
            with open(round2_response_without_images_file, "r", encoding="utf-8") as f:
                response_content = f.read()
            # 用正则提取所有组的详细内容（包括分隔线、组号、学生答案、正确答案、模型回答）
            group_pattern = re.compile(r"(=+\n处理第 (\d+) 组JSON响应\n\n=+\n[\s\S]*?)(?==+\n|\Z)")
            all_groups = group_pattern.findall(response_content)
            # 只保留错题组（确保类型一致）
            wrong_set = set(str(i) for i in wrong_items)
            print(f"all_groups count: {len(all_groups)}")
            print(f"wrong_items: {wrong_items}")
            print(f"wrong_set: {wrong_set}")
            print(f"all_groups group numbers: {[g[1] for g in all_groups]}")
            wrong_details = [g[0] for g in all_groups if g[1] in wrong_set]
            print(f"wrong_details count: {len(wrong_details)}")
            with open(summary_file, "w", encoding="utf-8") as f:
                # 头部结构与 round2_response_without_images 一致
                f.write(f"## 错题\n\n")
                if wrong_details:
                    for detail in wrong_details:
                        # 提取组号
                        match = re.search(r"处理第 (\d+) 组JSON响应", detail)
                        if match:
                            f.write(f"- 第 {match.group(1)} 组响应\n")
                else:
                    f.write("本次无错题。\n")
                f.write(f"\n## 准确率：{(accuracy*100):.2f}%  （({total} - {wrong_count}) / {total}）\n\n")
                f.write(f"# 运行时间: {now}\n\n")
                if grading_mode == "model":
                    f.write(f"**批改方式：** 模型交互\n\n")
                    f.write(f"**使用模型ID：** {selected_model}\n\n")
                    f.write(f"## 使用的prompt\n\n{round2_prompt_without_images_text}\n\n")
                else:
                    f.write(f"**批改方式：** JSON比对\n\n")
                    f.write(f"**比对说明：** 直接比对学生答案和正确答案的JSON字符串\n\n")
                # 错题详细内容
                if wrong_details:
                    for detail in wrong_details:
                        f.write(detail)
                        f.write("\n")
            print(f"已创建错题详细 summary 文件: {summary_file}")

    print(f"结果已保存到：{round2_response_without_images_file}")