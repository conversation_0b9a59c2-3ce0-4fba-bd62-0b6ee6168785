#!/usr/bin/env python3
"""
检查所有题型的round2 response_template是否正确
比对学生答案和正确答案，验证模型判断的true/false是否正确

使用方法:
1. 运行脚本: python check_all_response_templates.py
2. 选择要检查的题型（1-8）或选择检查所有题型（9）
3. 脚本会自动比对学生答案和正确答案，检查模型判断是否正确

注意:
- 只进行严格的字符串比较，不处理任何数学等价或格式转换
- 如果学生答案和正确答案字符串完全相同，期望模型判断为true
- 如果学生答案和正确答案字符串不同，期望模型判断为false
- 会报告所有不符合预期的判断结果
"""

import os
import re
import json
from pathlib import Path
from datetime import datetime

def get_available_types():
    """获取所有可用的题型"""
    types_dir = Path("types")
    if not types_dir.exists():
        return []

    # 题型名称映射
    type_name_map = {
        'danxuanti': '单选题',
        'jiandandesizeyunsuan': '简单的四则运算',
        'panduanti': '判断题',
        'shuxuejisuanti': '数学计算题',
        'shuxueyingyongti': '数学应用题',
        'tiankongti': '填空题',
        'tukapanduanti': '图卡判断题',
        'tukaxuanzeti': '图卡选择题'
    }

    available_types = []
    for item in types_dir.iterdir():
        if item.is_dir():
            # 检查是否有必要的文件
            response_template = item / "response" / "response_template.md"
            answer_file = item / "response" / "answer.md"
            round2_template = item / "round2_response_with_images" / "response_template.md"

            if response_template.exists() and answer_file.exists() and round2_template.exists():
                folder_name = item.name
                display_name = type_name_map.get(folder_name, folder_name)
                available_types.append((folder_name, display_name))

    return sorted(available_types, key=lambda x: x[1])

def load_json_from_md(file_path, pattern):
    """从markdown文件中提取JSON数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        matches = re.findall(pattern, content, re.DOTALL)
        return matches
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []

def compare_answers(student_answer, correct_answer):
    """比较学生答案和正确答案，返回应该的判断结果"""
    try:
        # 简单的字符串比较，不处理任何格式问题
        return str(student_answer).strip() == str(correct_answer).strip()
    except:
        return False

def update_round2_template(question_type, group_index, question_key, new_value):
    """更新round2_response_with_images_template.md中指定组的模型回答"""
    round2_template_path = Path("types") / question_type / "round2_response_with_images" / "response_template.md"

    try:
        with open(round2_template_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 找到对应组的模型回答部分
        pattern = r'(处理第 ' + str(group_index + 1) + r' 组JSON响应.*?### 模型回答：\s*```json\s*)(.*?)(\s*```)'
        match = re.search(pattern, content, re.DOTALL)

        if match:
            json_str = match.group(2)
            try:
                model_results = json.loads(json_str)
                model_results[question_key] = new_value
                new_json_str = json.dumps(model_results, ensure_ascii=False)

                new_content = content[:match.start(2)] + new_json_str + content[match.end(2):]

                with open(round2_template_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)

                return True
            except json.JSONDecodeError:
                print(f"❌ JSON解析错误，无法更新第{group_index + 1}组")
                return False
        else:
            print(f"❌ 找不到第{group_index + 1}组的模型回答部分")
            return False
    except Exception as e:
        print(f"❌ 更新文件时出错: {e}")
        return False

def generate_check_report(question_type, total_questions, all_errors, modified_groups, unmodified_errors):
    """生成检查报告"""
    # 创建报告文件夹
    report_dir = Path("types") / question_type / "check_response_template"
    report_dir.mkdir(exist_ok=True)

    # 生成时间戳文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = report_dir / f"check_report_{timestamp}.md"

    # 生成报告内容
    report_content = f"""# Round2 Response Template 检查报告

## 检查信息
- **题型**: {question_type}
- **检查时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **总题目数**: {total_questions}
- **发现错误数**: {len(all_errors)}
- **修改组数**: {len(modified_groups)}
- **未修改错误数**: {len(unmodified_errors)}

## 检查结果概览
- ✅ **正确题目**: {total_questions - len(all_errors)} 个
- ❌ **错误题目**: {len(all_errors)} 个
- 🔧 **已修改**: {len(all_errors) - len(unmodified_errors)} 个
- ⏭️ **未修改**: {len(unmodified_errors)} 个

## 所有发现的错误

"""

    if all_errors:
        # 按组分组所有错误
        errors_by_group = {}
        for error in all_errors:
            group = error['group']
            if group not in errors_by_group:
                errors_by_group[group] = []
            errors_by_group[group].append(error)

        for group in sorted(errors_by_group.keys()):
            group_errors = errors_by_group[group]
            error_questions = [e['question'] for e in group_errors]

            # 判断这个组是否被修改
            if group in modified_groups:
                status = "🔧 已修改"
            else:
                status = "⏭️ 未修改"

            report_content += f"### 第{group}组 ({', '.join(error_questions)}) - {status}\n\n"

            for error in group_errors:
                report_content += f"- **{error['question']}**: `'{error['student_answer']}'` vs `'{error['correct_answer']}'` → 模型:{error['model_result']}, 应该:{error['expected_result']}\n"

            report_content += "\n"
    else:
        report_content += "✅ 没有发现任何错误！\n\n"

    # 添加未修改错误汇总
    if unmodified_errors:
        report_content += "## 未修改的错误汇总\n\n"

        # 按组重新分组未修改的错误
        unmodified_by_group = {}
        for error in unmodified_errors:
            group = error['group']
            if group not in unmodified_by_group:
                unmodified_by_group[group] = []
            unmodified_by_group[group].append(error)

        for group in sorted(unmodified_by_group.keys()):
            group_errors = unmodified_by_group[group]
            error_questions = [e['question'] for e in group_errors]
            report_content += f"### 第{group}组 ({', '.join(error_questions)})\n\n"

            for error in group_errors:
                report_content += f"- **{error['question']}**: `'{error['student_answer']}'` vs `'{error['correct_answer']}'` → 模型:{error['model_result']}, 应该:{error['expected_result']}\n"

            report_content += "\n"
    else:
        report_content += "## 未修改的错误汇总\n\n✅ 所有错误都已修改！\n\n"

    # 添加检查说明
    report_content += f"""## 检查说明

### 数据源
- **学生答案**: `types/{question_type}/response/response_template.md` 中的"响应内容"
- **正确答案**: `types/{question_type}/response/answer.md` 中的"响应内容"
- **模型判断**: `types/{question_type}/round2_response_with_images/response_template.md` 中的"模型回答"

### 比对规则
- 按顺序比对JSON中的value，忽略键名差异
- 学生答案与正确答案字符串完全相同 → 期望判断为true
- 学生答案与正确答案字符串不相同 → 期望判断为false

### 修改说明
- 🔧 **已修改**: 表示该组的错误已通过脚本自动修改
- ⏭️ **未修改**: 表示该组的错误被用户跳过，未进行修改

---
*报告生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""

    # 写入报告文件
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)

    return report_path

def check_response_template(question_type):
    """检查指定题型的response_template"""
    print(f"\n正在检查题型: {question_type}")
    print("=" * 50)
    
    # 文件路径
    base_path = Path("types") / question_type
    response_template_path = base_path / "response" / "response_template.md"
    answer_path = base_path / "response" / "answer.md"
    round2_template_path = base_path / "round2_response_with_images" / "response_template.md"
    
    # 提取数据的正则表达式模式
    response_pattern = r'### 响应内容：\s*```json\s*(.*?)\s*```'
    answer_pattern = r'### 响应内容：\s*```json\s*(.*?)\s*```'
    round2_pattern = r'### 学生答案：\s*```json\s*(.*?)\s*```.*?### 正确答案：\s*```json\s*(.*?)\s*```.*?### 模型回答：\s*```json\s*(.*?)\s*```'
    
    # 加载数据
    print("正在加载数据...")
    response_data = load_json_from_md(response_template_path, response_pattern)
    answer_data = load_json_from_md(answer_path, answer_pattern)
    round2_matches = re.findall(round2_pattern, round2_template_path.read_text(encoding='utf-8'), re.DOTALL)
    
    print(f"找到 {len(response_data)} 个学生答案")
    print(f"找到 {len(answer_data)} 个正确答案")
    print(f"找到 {len(round2_matches)} 个round2比对结果")
    
    if len(response_data) != len(answer_data):
        print("⚠️  警告: 学生答案和正确答案数量不匹配!")
        return
    
    errors = []
    total_questions = 0
    
    # 检查每个round2的比对结果
    for i, (_, _, model_json) in enumerate(round2_matches):
        try:
            # 获取对应的学生答案（来自response_template）和正确答案（来自answer）
            if i >= len(response_data) or i >= len(answer_data):
                print(f"⚠️  警告: 第{i+1}组数据索引超出范围")
                continue

            student_answers = json.loads(response_data[i])  # 真正的学生答案
            correct_answers = json.loads(answer_data[i])    # 正确答案
            model_results = json.loads(model_json)          # 模型判断结果

            # 按顺序比对value，忽略键名差异
            student_values = list(student_answers.values())
            correct_values = list(correct_answers.values())
            model_values = list(model_results.values())

            # 确保三个列表长度一致
            min_length = min(len(student_values), len(correct_values), len(model_values))

            for j in range(min_length):
                student_ans = student_values[j]
                correct_ans = correct_values[j]
                model_result = model_values[j]

                # 计算应该的结果
                expected_result = compare_answers(student_ans, correct_ans)
                total_questions += 1

                # 检查是否有错误
                if model_result != expected_result:
                    errors.append({
                        'group': i + 1,
                        'question': f'题目{j+1}',
                        'student_answer': student_ans,
                        'correct_answer': correct_ans,
                        'model_result': model_result,
                        'expected_result': expected_result
                    })
        
        except Exception as e:
            print(f"❌ 处理第{i+1}组数据时出错: {e}")
            continue
    
    # 输出结果
    print(f"\n检查完成!")
    print(f"总共检查了 {total_questions} 个题目")

    if errors:
        print(f"❌ 发现 {len(errors)} 个错误:")
        print("-" * 50)

        # 按组分组错误
        errors_by_group = {}
        for error in errors:
            group = error['group']
            if group not in errors_by_group:
                errors_by_group[group] = []
            errors_by_group[group].append(error)

        # 交互式处理错误
        unmodified_errors = []
        modified_groups = set()  # 跟踪被修改的组

        for group in sorted(errors_by_group.keys()):
            group_errors = errors_by_group[group]
            error_questions = [e['question'] for e in group_errors]
            print(f"\n第{group}组 ({', '.join(error_questions)}):")

            for error in group_errors:
                print(f"  {error['question']}: '{error['student_answer']}' vs '{error['correct_answer']}' -> 模型:{error['model_result']}, 应该:{error['expected_result']}")

            # 询问用户是否修改
            while True:
                try:
                    choice = input(f"是否修改第{group}组的错误? (1=修改, 2=不修改, 3=修改部分题目): ").strip()
                    if choice == '1':
                        # 修改该组的所有错误
                        success_count = 0
                        for error in group_errors:
                            # 找到对应的题目键名
                            question_index = int(error['question'].replace('题目', '')) - 1

                            # 尝试不同的键名格式
                            possible_keys = [
                                f"题目{question_index + 1}",
                                f"题目 {question_index + 1}"
                            ]

                            updated = False
                            for key in possible_keys:
                                if update_round2_template(question_type, error['group'] - 1, key, error['expected_result']):
                                    updated = True
                                    break

                            if updated:
                                success_count += 1
                            else:
                                unmodified_errors.append(error)

                        print(f"✅ 成功修改第{group}组中的 {success_count}/{len(group_errors)} 个错误")
                        if success_count > 0:
                            modified_groups.add(group)
                        break
                    elif choice == '2':
                        # 不修改，添加到未修改列表
                        unmodified_errors.extend(group_errors)
                        print(f"⏭️  跳过第{group}组")
                        break
                    elif choice == '3':
                        # 修改部分题目
                        available_questions = [int(e['question'].replace('题目', '')) for e in group_errors]
                        print(f"可修改的题目: {', '.join(map(str, available_questions))}")

                        while True:
                            try:
                                question_input = input("请输入需要修改的题目编号（用空格分隔）: ").strip()
                                if not question_input:
                                    print("❌ 请输入题目编号")
                                    continue

                                selected_questions = []
                                for q in question_input.split():
                                    try:
                                        q_num = int(q)
                                        if q_num in available_questions:
                                            selected_questions.append(q_num)
                                        else:
                                            print(f"❌ 题目{q_num}不在错误列表中")
                                            raise ValueError()
                                    except ValueError:
                                        print(f"❌ 无效的题目编号: {q}")
                                        raise ValueError()

                                if selected_questions:
                                    break
                                else:
                                    print("❌ 请至少选择一个题目")
                            except ValueError:
                                continue

                        # 修改选中的题目
                        success_count = 0
                        for error in group_errors:
                            question_num = int(error['question'].replace('题目', ''))
                            if question_num in selected_questions:
                                # 修改这个题目
                                question_index = question_num - 1

                                # 尝试不同的键名格式
                                possible_keys = [
                                    f"题目{question_index + 1}",
                                    f"题目 {question_index + 1}"
                                ]

                                updated = False
                                for key in possible_keys:
                                    if update_round2_template(question_type, error['group'] - 1, key, error['expected_result']):
                                        updated = True
                                        break

                                if updated:
                                    success_count += 1
                                else:
                                    unmodified_errors.append(error)
                            else:
                                # 不修改这个题目，添加到未修改列表
                                unmodified_errors.append(error)

                        print(f"✅ 成功修改第{group}组中的 {success_count}/{len(selected_questions)} 个选中题目")
                        if success_count > 0:
                            modified_groups.add(group)
                        break
                    else:
                        print("❌ 请输入 1、2 或 3")
                except KeyboardInterrupt:
                    print("\n\n用户取消操作")
                    return errors

        # 显示未修改的错误
        if unmodified_errors:
            print(f"\n📋 未修改的错误 ({len(unmodified_errors)} 个):")
            print("-" * 50)

            # 按组重新分组未修改的错误
            unmodified_by_group = {}
            for error in unmodified_errors:
                group = error['group']
                if group not in unmodified_by_group:
                    unmodified_by_group[group] = []
                unmodified_by_group[group].append(error)

            for group in sorted(unmodified_by_group.keys()):
                group_errors = unmodified_by_group[group]
                error_questions = [e['question'] for e in group_errors]
                print(f"第{group}组 ({', '.join(error_questions)}):")

                for error in group_errors:
                    print(f"  {error['question']}: '{error['student_answer']}' vs '{error['correct_answer']}' -> 模型:{error['model_result']}, 应该:{error['expected_result']}")
                print()
        else:
            print("\n✅ 所有错误都已修改!")
    else:
        print("✅ 没有发现错误! 所有判断都是正确的。")
        modified_groups = set()

    # 生成检查报告
    report_path = generate_check_report(question_type, total_questions, errors, modified_groups, unmodified_errors)
    print(f"\n📄 检查报告已生成: {report_path}")

    return errors

def main():
    """主函数"""
    print("Round2 Response Template 检查工具")
    print("=" * 50)

    # 获取可用的题型
    available_types = get_available_types()

    if not available_types:
        print("❌ 没有找到可用的题型!")
        print("请确保在types目录下有正确的文件结构:")
        print("  types/题型名/response/response_template.md")
        print("  types/题型名/response/answer.md")
        print("  types/题型名/round2_response_with_images/response_template.md")
        return

    print("可用的题型:")
    for i, (folder_name, display_name) in enumerate(available_types, 1):
        print(f"  {i}. {display_name}")

    # 用户选择
    try:
        choice = input(f"\n请选择要检查的题型 (1-{len(available_types)}): ").strip()
        choice_num = int(choice)

        if 1 <= choice_num <= len(available_types):
            # 检查指定题型
            selected_folder, selected_display = available_types[choice_num - 1]
            check_response_template(selected_folder)

        else:
            print("❌ 无效的选择!")

    except ValueError:
        print("❌ 请输入有效的数字!")
    except KeyboardInterrupt:
        print("\n\n用户取消操作")

if __name__ == "__main__":
    main()
